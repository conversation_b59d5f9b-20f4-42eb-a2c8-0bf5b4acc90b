// 引入必要的库
const { Client } = require('discord.js-selfbot-v13');
const fetch = require('node-fetch');
const FormData = require('form-data');
const { generateText } = require('ai');
const { createOpenAI } = require('@ai-sdk/openai');
const { Bot } = require('grammy');

// 读取配置文件
const config = require('./config.js');

// 初始化 OpenAI 客户端
const openai = createOpenAI({
  baseURL: config.ai.baseURL,
  apiKey: config.ai.apiKey,
});

// 创建客户端实例
const client = new Client({
  checkUpdate: false,
});

// 创建 Telegram Bot 实例
let telegramBot = null;
if (config.botToken) {
  telegramBot = new Bot(config.botToken);
  console.log('Telegram Bot 已初始化');
} else {
  console.log('未配置 Telegram Bot Token，跳过 Telegram 功能');
}

// 创建一个函数来处理非流式请求
async function getAIResponse(prompt) {
  const systemPrompt = `你是语言文本处理专家 你的任务是把用户传入的语言分以下步骤进行处理成字符串
1.如果内容中的有https链接，将链接过滤掉
2.如果有与链接关联的无意义文字（如"Disclaimer: https://..."），也一并过滤掉
3.保留所有"@xxx"和"$xxx"格式的内容，这些是命令或标识符，不要修改或翻译
4.翻译处理：
  - 如果文本主要是中文，则不需要翻译，直接输出过滤后的原文内容  (第一部分)
  - 如果文本主要是英文或其他语言，将其翻译成地道的中文  (第二部分)
5. 输出格式：
  - 第一部分： 输出处理后的原文
  - 第二部分： 翻译后的内容
  - 一二部分之间用2个换行符分隔
  - 如果第二部分和第一部分语言相似度高则只输出第一部分，第二部分为空字符串即可
  - 不要输出任何额外的解释或说明
`;
  try {
    const { text } = await generateText({
      model: openai(config.ai.model),
      temperature: config.ai.temperature,
      maxTokens: config.ai.maxTokens,
      messages: [
        {role: 'system', content: systemPrompt},
        {role: 'user', content: prompt},
      ]
    })

    return text;
  } catch (error) {
    console.error('AI 请求失败:', error);
    return {
      text: '处理失败',
      translation: '处理失败',
      error: error.message
    };
  }
}

// 获取频道对应的webhook config
function getWebhookConfig(channelId) {
  const channel = config.channels.find(ch => ch.sourceId === channelId);
  return channel ? channel : null;
}

// 获取频道对应的 Telegram 配置
function getTelegramConfig(channelId) {
  const channel = config.tgChannels.find(ch => ch.sourceId === channelId);
  return channel ? channel : null;
}

// 发送消息到 Telegram
async function sendToTelegram(targetId, content, files = []) {
  if (!telegramBot) {
    console.log('Telegram Bot 未初始化，跳过发送');
    return;
  }

  try {
    // 检查是否包含禁用词
    if (config.tgBanWords.some(word => content.includes(word))) {
      console.log(`Telegram 消息包含屏蔽词，已忽略处理`);
      return;
    }

    // 如果有文件附件，过滤出图片和视频
    if (files && files.length > 0) {
      // 过滤出图片和视频文件
      const mediaFiles = files.filter(file =>
        file.contentType && (
          file.contentType.startsWith('image/') ||
          file.contentType.startsWith('video/')
        )
      );

      if (mediaFiles.length > 0) {
        try {
          // 统一使用 media group 形式发送（支持单个或多个媒体文件）
          const mediaGroup = await Promise.all(
            mediaFiles.map(async (file, index) => {
              const response = await fetch(file.url);
              const buffer = await response.buffer();

              const mediaItem = {
                type: file.contentType.startsWith('image/') ? 'photo' : 'video',
                media: buffer
              };

              // 只在第一个媒体项添加 caption
              if (index === 0 && content) {
                mediaItem.caption = content;
              }

              return mediaItem;
            })
          );

          await telegramBot.api.sendMediaGroup(targetId, mediaGroup);
        } catch (fileError) {
          console.error(`发送媒体文件到 Telegram 失败:`, fileError);
          // 如果媒体发送失败，至少发送文本
          if (content) {
            await telegramBot.api.sendMessage(targetId, content);
          }
        }
      } else {
        // 没有图片或视频文件，只发送文本
        if (content) {
          await telegramBot.api.sendMessage(targetId, content);
        }
      }
    } else if (content) {
      // 只发送文本消息
      await telegramBot.api.sendMessage(targetId, content);
    }

    console.log(`已发送到 Telegram 频道: ${targetId}`);
  } catch (error) {
    console.error(`发送到 Telegram 失败:`, error);
  }
}

// 当用户账号准备就绪时
client.on('ready', async () => {
  console.log(`已登录为 ${client.user.tag}!`);
  console.log('监听的频道:');
  for (const channel of config.channels) {
    try {
      const ch = await client.channels.fetch(channel.sourceId);
      console.log(`- ${ch.name} (${channel.sourceId})`);
    } catch (error) {
      console.log(`- 未知频道 (${channel.sourceId})`);
    }
  }
});

// 消息处理
client.on('messageCreate', async (message) => {
  try {
    const webhookConfig = getWebhookConfig(message.channelId);
    const telegramConfig = getTelegramConfig(message.channelId);

    // 如果既没有 webhook 配置也没有 telegram 配置，直接返回
    if (!webhookConfig && !telegramConfig) {
      return;
    }

    const username = `${message.author.globalName || message.author.username}`;
    const avatarUrl = message.author.displayAvatarURL({ format: 'png', dynamic: true });
    const content = message.content || '';
    const embeds = message.embeds || [];
    // 获取频道信息
    const channelName = message.channel.name || '未知频道';
    console.log(`收到来自频道【${channelName}】 (${message.channelId}) 的消息`);

    // 检查 Discord 屏蔽词（只对 webhook 转发有效）
    const shouldSkipWebhook = webhookConfig && config.banWords.some(word => content.includes(word));
    if (shouldSkipWebhook) {
      console.log(`包含 Discord 屏蔽词，已忽略 webhook 处理`);
    }

    // 处理 Discord webhook 转发
    if (webhookConfig && !shouldSkipWebhook) {
      // 只转发文字判断
      const attachments = webhookConfig.onlyText ? [] : Array.from(message.attachments.values());

      let resultText = '';
      if (webhookConfig.useTranslate && content.length) {
        // AI处理内容并翻译
        // 记录开始时间
        const startTime = Date.now();
        resultText = await getAIResponse(content);
        console.log('[ Discord resultText ] >', resultText)
        // 计算运行时间（毫秒转换为秒）
        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000;
        console.log(`AI响应处理耗时: ${executionTime}秒`);
      } else {
        resultText = content;
      }

      try {
        await sendToWebhook(webhookConfig.webhookUrl, resultText, username, avatarUrl, embeds, attachments);
        console.log(`已转发到对应的 Discord webhook ${message.channelId}`);
      } catch (error) {
        console.error(`Discord webhook 转发失败: ${error.message}`);
      }
    }

    // 处理 Telegram 转发
    if (telegramConfig) {
      const attachments = Array.from(message.attachments.values());
      console.log(attachments)

      let telegramText = '';
      if (telegramConfig.useTranslate && content.length) {
        // AI处理内容并翻译
        // 记录开始时间
        const startTime = Date.now();
        telegramText = await getAIResponse(content);
        console.log('[ Telegram resultText ] >', telegramText)
        // 计算运行时间（毫秒转换为秒）
        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000;
        console.log(`Telegram AI响应处理耗时: ${executionTime}秒`);
      } else {
        telegramText = content;
      }

      // 添加用户信息前缀

      try {
        await sendToTelegram(telegramConfig.targetId, telegramText, attachments);
        console.log(`已转发到 Telegram 频道 ${telegramConfig.targetId}`);
      } catch (error) {
        console.error(`Telegram 转发失败: ${error.message}`);
      }
    }
  } catch (error) {
    console.error(`处理消息时出错: ${error.message}`);
  }
});

// 发送消息到webhook
async function sendToWebhook(webhookUrl, resultText, username, avatarUrl, embeds = [], files = []) {
  try {
    // 构建基本请求数据
    const payload = {
      content: resultText || '',
      username: username || 'Discord转发',
      avatar_url: avatarUrl || '',
      embeds: embeds
    };

    // 如果没有文件，直接发送JSON
    if (!files.length) {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Webhook响应错误: ${response.status}`);
      }

      return;
    }

    // 如果有文件，需要使用FormData
    const formData = new FormData();
    formData.append('payload_json', JSON.stringify(payload));

    // 添加所有文件
    for (let i = 0; i < files.length; i++) {
      const attachment = files[i];
      const response = await fetch(attachment.url);
      const buffer = await response.buffer();
      
      formData.append(`file${i}`, buffer, {
        filename: attachment.name,
        contentType: attachment.contentType
      });
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Webhook文件上传响应错误: ${response.status}`);
    }
  } catch (error) {
    console.error('发送到Webhook失败:', error);
    throw error;
  }
}

// 登录
client.login(config.token);

