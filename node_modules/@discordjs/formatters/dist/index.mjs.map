{"version": 3, "sources": ["../src/escapers.ts", "../src/formatters.ts", "../src/index.ts"], "sourcesContent": ["/* eslint-disable prefer-named-capture-group */\n\n/**\n * The options that affect what will be escaped.\n */\nexport interface EscapeMarkdownOptions {\n\t/**\n\t * Whether to escape bold text.\n\t *\n\t * @defaultValue `true`\n\t */\n\tbold?: boolean;\n\n\t/**\n\t * Whether to escape bulleted lists.\n\t *\n\t * @defaultValue `false`\n\t */\n\tbulletedList?: boolean;\n\n\t/**\n\t * Whether to escape code blocks.\n\t *\n\t * @defaultValue `true`\n\t */\n\tcodeBlock?: boolean;\n\n\t/**\n\t * Whether to escape text inside code blocks.\n\t *\n\t * @defaultValue `true`\n\t */\n\tcodeBlockContent?: boolean;\n\n\t/**\n\t * Whether to escape `\\`.\n\t *\n\t * @defaultValue `true`\n\t */\n\tescape?: boolean;\n\n\t/**\n\t * Whether to escape headings.\n\t *\n\t * @defaultValue `false`\n\t */\n\theading?: boolean;\n\n\t/**\n\t * Whether to escape inline code.\n\t *\n\t * @defaultValue `true`\n\t */\n\tinlineCode?: boolean;\n\n\t/**\n\t * Whether to escape text inside inline code.\n\t *\n\t * @defaultValue `true`\n\t */\n\tinlineCodeContent?: boolean;\n\t/**\n\t * Whether to escape italics.\n\t *\n\t * @defaultValue `true`\n\t */\n\titalic?: boolean;\n\n\t/**\n\t * Whether to escape masked links.\n\t *\n\t * @defaultValue `false`\n\t */\n\tmaskedLink?: boolean;\n\n\t/**\n\t * Whether to escape numbered lists.\n\t *\n\t * @defaultValue `false`\n\t */\n\tnumberedList?: boolean;\n\n\t/**\n\t * Whether to escape spoilers.\n\t *\n\t * @defaultValue `true`\n\t */\n\tspoiler?: boolean;\n\n\t/**\n\t * Whether to escape strikethroughs.\n\t *\n\t * @defaultValue `true`\n\t */\n\tstrikethrough?: boolean;\n\n\t/**\n\t * Whether to escape underlines.\n\t *\n\t * @defaultValue `true`\n\t */\n\tunderline?: boolean;\n}\n\n/**\n * Escapes any Discord-flavored markdown in a string.\n *\n * @param text - Content to escape\n * @param options - Options for escaping the markdown\n */\nexport function escapeMarkdown(text: string, options: EscapeMarkdownOptions = {}): string {\n\tconst {\n\t\tcodeBlock = true,\n\t\tinlineCode = true,\n\t\tbold = true,\n\t\titalic = true,\n\t\tunderline = true,\n\t\tstrikethrough = true,\n\t\tspoiler = true,\n\t\tcodeBlockContent = true,\n\t\tinlineCodeContent = true,\n\t\tescape = true,\n\t\theading = false,\n\t\tbulletedList = false,\n\t\tnumberedList = false,\n\t\tmaskedLink = false,\n\t} = options;\n\n\tif (!codeBlockContent) {\n\t\treturn text\n\t\t\t.split('```')\n\t\t\t.map((subString, index, array) => {\n\t\t\t\tif (index % 2 && index !== array.length - 1) return subString;\n\t\t\t\treturn escapeMarkdown(subString, {\n\t\t\t\t\tinlineCode,\n\t\t\t\t\tbold,\n\t\t\t\t\titalic,\n\t\t\t\t\tunderline,\n\t\t\t\t\tstrikethrough,\n\t\t\t\t\tspoiler,\n\t\t\t\t\tinlineCodeContent,\n\t\t\t\t\tescape,\n\t\t\t\t\theading,\n\t\t\t\t\tbulletedList,\n\t\t\t\t\tnumberedList,\n\t\t\t\t\tmaskedLink,\n\t\t\t\t});\n\t\t\t})\n\t\t\t.join(codeBlock ? '\\\\`\\\\`\\\\`' : '```');\n\t}\n\n\tif (!inlineCodeContent) {\n\t\treturn text\n\t\t\t.split(/(?<=^|[^`])`(?=[^`]|$)/g)\n\t\t\t.map((subString, index, array) => {\n\t\t\t\tif (index % 2 && index !== array.length - 1) return subString;\n\t\t\t\treturn escapeMarkdown(subString, {\n\t\t\t\t\tcodeBlock,\n\t\t\t\t\tbold,\n\t\t\t\t\titalic,\n\t\t\t\t\tunderline,\n\t\t\t\t\tstrikethrough,\n\t\t\t\t\tspoiler,\n\t\t\t\t\tescape,\n\t\t\t\t\theading,\n\t\t\t\t\tbulletedList,\n\t\t\t\t\tnumberedList,\n\t\t\t\t\tmaskedLink,\n\t\t\t\t});\n\t\t\t})\n\t\t\t.join(inlineCode ? '\\\\`' : '`');\n\t}\n\n\tlet res = text;\n\tif (escape) res = escapeEscape(res);\n\tif (inlineCode) res = escapeInlineCode(res);\n\tif (codeBlock) res = escapeCodeBlock(res);\n\tif (italic) res = escapeItalic(res);\n\tif (bold) res = escapeBold(res);\n\tif (underline) res = escapeUnderline(res);\n\tif (strikethrough) res = escapeStrikethrough(res);\n\tif (spoiler) res = escapeSpoiler(res);\n\tif (heading) res = escapeHeading(res);\n\tif (bulletedList) res = escapeBulletedList(res);\n\tif (numberedList) res = escapeNumberedList(res);\n\tif (maskedLink) res = escapeMaskedLink(res);\n\treturn res;\n}\n\n/**\n * Escapes code block markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeCodeBlock(text: string): string {\n\treturn text.replaceAll('```', '\\\\`\\\\`\\\\`');\n}\n\n/**\n * Escapes inline code markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeInlineCode(text: string): string {\n\treturn text.replaceAll(/(?<=^|[^`])``?(?=[^`]|$)/g, (match) => (match.length === 2 ? '\\\\`\\\\`' : '\\\\`'));\n}\n\n/**\n * Escapes italic markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeItalic(text: string): string {\n\tlet idx = 0;\n\tconst newText = text.replaceAll(/(?<=^|[^*])\\*([^*]|\\*\\*|$)/g, (_, match) => {\n\t\tif (match === '**') return ++idx % 2 ? `\\\\*${match}` : `${match}\\\\*`;\n\t\treturn `\\\\*${match}`;\n\t});\n\tidx = 0;\n\treturn newText.replaceAll(/(?<=^|[^_])(?<!<a?:.+|https?:\\/\\/\\S+)_(?!:\\d+>)([^_]|__|$)/g, (_, match) => {\n\t\tif (match === '__') return ++idx % 2 ? `\\\\_${match}` : `${match}\\\\_`;\n\t\treturn `\\\\_${match}`;\n\t});\n}\n\n/**\n * Escapes bold markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeBold(text: string): string {\n\tlet idx = 0;\n\treturn text.replaceAll(/\\*\\*(\\*)?/g, (_, match) => {\n\t\tif (match) return ++idx % 2 ? `${match}\\\\*\\\\*` : `\\\\*\\\\*${match}`;\n\t\treturn '\\\\*\\\\*';\n\t});\n}\n\n/**\n * Escapes underline markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeUnderline(text: string): string {\n\tlet idx = 0;\n\treturn text.replaceAll(/(?<!<a?:.+|https?:\\/\\/\\S+)__(_)?(?!:\\d+>)/g, (_, match) => {\n\t\tif (match) return ++idx % 2 ? `${match}\\\\_\\\\_` : `\\\\_\\\\_${match}`;\n\t\treturn '\\\\_\\\\_';\n\t});\n}\n\n/**\n * Escapes strikethrough markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeStrikethrough(text: string): string {\n\treturn text.replaceAll('~~', '\\\\~\\\\~');\n}\n\n/**\n * Escapes spoiler markdown in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeSpoiler(text: string): string {\n\treturn text.replaceAll('||', '\\\\|\\\\|');\n}\n\n/**\n * Escapes escape characters in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeEscape(text: string): string {\n\treturn text.replaceAll('\\\\', '\\\\\\\\');\n}\n\n/**\n * Escapes heading characters in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeHeading(text: string): string {\n\treturn text.replaceAll(/^( {0,2})([*-] )?( *)(#{1,3} )/gm, '$1$2$3\\\\$4');\n}\n\n/**\n * Escapes bulleted list characters in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeBulletedList(text: string): string {\n\treturn text.replaceAll(/^( *)([*-])( +)/gm, '$1\\\\$2$3');\n}\n\n/**\n * Escapes numbered list characters in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeNumberedList(text: string): string {\n\treturn text.replaceAll(/^( *\\d+)\\./gm, '$1\\\\.');\n}\n\n/**\n * Escapes masked link characters in a string.\n *\n * @param text - Content to escape\n */\nexport function escapeMaskedLink(text: string): string {\n\treturn text.replaceAll(/\\[.+]\\(.+\\)/gm, '\\\\$&');\n}\n", "import type { URL } from 'node:url';\nimport type { Snowflake } from 'discord-api-types/globals';\n\n/**\n * Wraps the content inside a code block with no language.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function codeBlock<Content extends string>(content: Content): `\\`\\`\\`\\n${Content}\\n\\`\\`\\``;\n\n/**\n * Wraps the content inside a code block with the specified language.\n *\n * @typeParam Language - This is inferred by the supplied language\n * @typeParam Content - This is inferred by the supplied content\n * @param language - The language for the code block\n * @param content - The content to wrap\n */\nexport function codeBlock<Language extends string, Content extends string>(\n\tlanguage: Language,\n\tcontent: Content,\n): `\\`\\`\\`${Language}\\n${Content}\\n\\`\\`\\``;\n\nexport function codeBlock(language: string, content?: string): string {\n\treturn content === undefined ? `\\`\\`\\`\\n${language}\\n\\`\\`\\`` : `\\`\\`\\`${language}\\n${content}\\n\\`\\`\\``;\n}\n\n/**\n * Wraps the content inside \\`backticks\\` which formats it as inline code.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function inlineCode<Content extends string>(content: Content): `\\`${Content}\\`` {\n\treturn `\\`${content}\\``;\n}\n\n/**\n * Formats the content into italic text.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function italic<Content extends string>(content: Content): `_${Content}_` {\n\treturn `_${content}_`;\n}\n\n/**\n * Formats the content into bold text.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function bold<Content extends string>(content: Content): `**${Content}**` {\n\treturn `**${content}**`;\n}\n\n/**\n * Formats the content into underscored text.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n * @deprecated Use {@link underline} instead.\n */\nexport function underscore<Content extends string>(content: Content): `__${Content}__` {\n\treturn underline(content);\n}\n\n/**\n * Formats the content into underlined text.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function underline<Content extends string>(content: Content): `__${Content}__` {\n\treturn `__${content}__`;\n}\n\n/**\n * Formats the content into strike-through text.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function strikethrough<Content extends string>(content: Content): `~~${Content}~~` {\n\treturn `~~${content}~~`;\n}\n\n/**\n * Formats the content into a quote.\n *\n * @remarks This needs to be at the start of the line for Discord to format it.\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function quote<Content extends string>(content: Content): `> ${Content}` {\n\treturn `> ${content}`;\n}\n\n/**\n * Formats the content into a block quote.\n *\n * @remarks This needs to be at the start of the line for Discord to format it.\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function blockQuote<Content extends string>(content: Content): `>>> ${Content}` {\n\treturn `>>> ${content}`;\n}\n\n/**\n * Wraps the URL into `<>` which stops it from embedding.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param url - The URL to wrap\n */\nexport function hideLinkEmbed<Content extends string>(url: Content): `<${Content}>`;\n\n/**\n * Wraps the URL into `<>` which stops it from embedding.\n *\n * @param url - The URL to wrap\n */\nexport function hideLinkEmbed(url: URL): `<${string}>`;\n\nexport function hideLinkEmbed(url: URL | string) {\n\treturn `<${url}>`;\n}\n\n/**\n * Formats the content and the URL into a masked URL.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to display\n * @param url - The URL the content links to\n */\nexport function hyperlink<Content extends string>(content: Content, url: URL): `[${Content}](${string})`;\n\n/**\n * Formats the content and the URL into a masked URL.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @typeParam Url - This is inferred by the supplied URL\n * @param content - The content to display\n * @param url - The URL the content links to\n */\nexport function hyperlink<Content extends string, Url extends string>(\n\tcontent: Content,\n\turl: Url,\n): `[${Content}](${Url})`;\n\n/**\n * Formats the content and the URL into a masked URL with a custom tooltip.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @typeParam Title - This is inferred by the supplied title\n * @param content - The content to display\n * @param url - The URL the content links to\n * @param title - The title shown when hovering on the masked link\n */\nexport function hyperlink<Content extends string, Title extends string>(\n\tcontent: Content,\n\turl: URL,\n\ttitle: Title,\n): `[${Content}](${string} \"${Title}\")`;\n\n/**\n * Formats the content and the URL into a masked URL with a custom tooltip.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @typeParam Url - This is inferred by the supplied URL\n * @typeParam Title - This is inferred by the supplied title\n * @param content - The content to display\n * @param url - The URL the content links to\n * @param title - The title shown when hovering on the masked link\n */\nexport function hyperlink<Content extends string, Url extends string, Title extends string>(\n\tcontent: Content,\n\turl: Url,\n\ttitle: Title,\n): `[${Content}](${Url} \"${Title}\")`;\n\nexport function hyperlink(content: string, url: URL | string, title?: string) {\n\treturn title ? `[${content}](${url} \"${title}\")` : `[${content}](${url})`;\n}\n\n/**\n * Formats the content into a spoiler.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function spoiler<Content extends string>(content: Content): `||${Content}||` {\n\treturn `||${content}||`;\n}\n\n/**\n * Formats a user id into a user mention.\n *\n * @typeParam UserId - This is inferred by the supplied user id\n * @param userId - The user id to format\n */\nexport function userMention<UserId extends Snowflake>(userId: UserId): `<@${UserId}>` {\n\treturn `<@${userId}>`;\n}\n\n/**\n * Formats a channel id into a channel mention.\n *\n * @typeParam ChannelId - This is inferred by the supplied channel id\n * @param channelId - The channel id to format\n */\nexport function channelMention<ChannelId extends Snowflake>(channelId: ChannelId): `<#${ChannelId}>` {\n\treturn `<#${channelId}>`;\n}\n\n/**\n * Formats a role id into a role mention.\n *\n * @typeParam RoleId - This is inferred by the supplied role id\n * @param roleId - The role id to format\n */\nexport function roleMention<RoleId extends Snowflake>(roleId: RoleId): `<@&${RoleId}>` {\n\treturn `<@&${roleId}>`;\n}\n\n/**\n * Formats an application command name, subcommand group name, subcommand name, and id into an application command mention.\n *\n * @typeParam CommandName - This is inferred by the supplied command name\n * @typeParam SubcommandGroupName - This is inferred by the supplied subcommand group name\n * @typeParam SubcommandName - This is inferred by the supplied subcommand name\n * @typeParam CommandId - This is inferred by the supplied command id\n * @param commandName - The application command name to format\n * @param subcommandGroupName - The subcommand group name to format\n * @param subcommandName - The subcommand name to format\n * @param commandId - The application command id to format\n */\nexport function chatInputApplicationCommandMention<\n\tCommandName extends string,\n\tSubcommandGroupName extends string,\n\tSubcommandName extends string,\n\tCommandId extends Snowflake,\n>(\n\tcommandName: CommandName,\n\tsubcommandGroupName: SubcommandGroupName,\n\tsubcommandName: SubcommandName,\n\tcommandId: CommandId,\n): `</${CommandName} ${SubcommandGroupName} ${SubcommandName}:${CommandId}>`;\n\n/**\n * Formats an application command name, subcommand name, and id into an application command mention.\n *\n * @typeParam CommandName - This is inferred by the supplied command name\n * @typeParam SubcommandName - This is inferred by the supplied subcommand name\n * @typeParam CommandId - This is inferred by the supplied command id\n * @param commandName - The application command name to format\n * @param subcommandName - The subcommand name to format\n * @param commandId - The application command id to format\n */\nexport function chatInputApplicationCommandMention<\n\tCommandName extends string,\n\tSubcommandName extends string,\n\tCommandId extends Snowflake,\n>(\n\tcommandName: CommandName,\n\tsubcommandName: SubcommandName,\n\tcommandId: CommandId,\n): `</${CommandName} ${SubcommandName}:${CommandId}>`;\n\n/**\n * Formats an application command name and id into an application command mention.\n *\n * @typeParam CommandName - This is inferred by the supplied command name\n * @typeParam CommandId - This is inferred by the supplied command id\n * @param commandName - The application command name to format\n * @param commandId - The application command id to format\n */\nexport function chatInputApplicationCommandMention<CommandName extends string, CommandId extends Snowflake>(\n\tcommandName: CommandName,\n\tcommandId: CommandId,\n): `</${CommandName}:${CommandId}>`;\n\nexport function chatInputApplicationCommandMention<\n\tCommandName extends string,\n\tSubcommandGroupName extends Snowflake | string,\n\tSubcommandName extends Snowflake | string,\n\tCommandId extends Snowflake,\n>(\n\tcommandName: CommandName,\n\tsubcommandGroupName: SubcommandGroupName,\n\tsubcommandName?: SubcommandName,\n\tcommandId?: CommandId,\n):\n\t| `</${CommandName} ${SubcommandGroupName} ${SubcommandName}:${CommandId}>`\n\t| `</${CommandName} ${SubcommandGroupName}:${SubcommandName}>`\n\t| `</${CommandName}:${SubcommandGroupName}>` {\n\tif (commandId !== undefined) {\n\t\treturn `</${commandName} ${subcommandGroupName} ${subcommandName!}:${commandId}>`;\n\t}\n\n\tif (subcommandName !== undefined) {\n\t\treturn `</${commandName} ${subcommandGroupName}:${subcommandName}>`;\n\t}\n\n\treturn `</${commandName}:${subcommandGroupName}>`;\n}\n\n/**\n * Formats a non-animated emoji id into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @param emojiId - The emoji id to format\n */\nexport function formatEmoji<EmojiId extends Snowflake>(emojiId: EmojiId, animated?: false): `<:_:${EmojiId}>`;\n\n/**\n * Formats an animated emoji id into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @param emojiId - The emoji id to format\n * @param animated - Whether the emoji is animated\n */\nexport function formatEmoji<EmojiId extends Snowflake>(emojiId: EmojiId, animated?: true): `<a:_:${EmojiId}>`;\n\n/**\n * Formats an emoji id into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @param emojiId - The emoji id to format\n * @param animated - Whether the emoji is animated\n */\nexport function formatEmoji<EmojiId extends Snowflake>(\n\temojiId: EmojiId,\n\tanimated?: boolean,\n): `<:_:${EmojiId}>` | `<a:_:${EmojiId}>`;\n\n/**\n * Formats a non-animated emoji id and name into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @typeParam EmojiName - This is inferred by the supplied name\n * @param options - The options for formatting an emoji\n */\nexport function formatEmoji<EmojiId extends Snowflake, EmojiName extends string>(\n\toptions: FormatEmojiOptions<EmojiId, EmojiName> & { animated: true },\n): `<a:${EmojiName}:${EmojiId}>`;\n\n/**\n * Formats an animated emoji id and name into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @typeParam EmojiName - This is inferred by the supplied name\n * @param options - The options for formatting an emoji\n */\nexport function formatEmoji<EmojiId extends Snowflake, EmojiName extends string>(\n\toptions: FormatEmojiOptions<EmojiId, EmojiName> & { animated?: false },\n): `<:${EmojiName}:${EmojiId}>`;\n\n/**\n * Formats an emoji id and name into a fully qualified emoji identifier.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @typeParam EmojiName - This is inferred by the supplied emoji name\n * @param options - The options for formatting an emoji\n */\nexport function formatEmoji<EmojiId extends Snowflake, EmojiName extends string>(\n\toptions: FormatEmojiOptions<EmojiId, EmojiName>,\n): `<:${EmojiName}:${EmojiId}>` | `<a:${EmojiName}:${EmojiId}>`;\n\nexport function formatEmoji<EmojiId extends Snowflake, EmojiName extends string>(\n\temojiIdOrOptions: EmojiId | FormatEmojiOptions<EmojiId, EmojiName>,\n\tanimated?: boolean,\n): `<:${string}:${EmojiId}>` | `<a:${string}:${EmojiId}>` {\n\tconst options =\n\t\ttypeof emojiIdOrOptions === 'string'\n\t\t\t? {\n\t\t\t\t\tid: emojiIdOrOptions,\n\t\t\t\t\tanimated: animated ?? false,\n\t\t\t\t}\n\t\t\t: emojiIdOrOptions;\n\n\tconst { id, animated: isAnimated, name: emojiName } = options;\n\n\treturn `<${isAnimated ? 'a' : ''}:${emojiName ?? '_'}:${id}>`;\n}\n\n/**\n * The options for formatting an emoji.\n *\n * @typeParam EmojiId - This is inferred by the supplied emoji id\n * @typeParam EmojiName - This is inferred by the supplied emoji name\n */\nexport interface FormatEmojiOptions<EmojiId extends Snowflake, EmojiName extends string> {\n\t/**\n\t * Whether the emoji is animated\n\t */\n\tanimated?: boolean;\n\t/**\n\t * The emoji id to format\n\t */\n\tid: EmojiId;\n\t/**\n\t * The name of the emoji\n\t */\n\tname?: EmojiName;\n}\n\n/**\n * Formats a channel link for a direct message channel.\n *\n * @typeParam ChannelId - This is inferred by the supplied channel id\n * @param channelId - The channel's id\n */\nexport function channelLink<ChannelId extends Snowflake>(\n\tchannelId: ChannelId,\n): `https://discord.com/channels/@me/${ChannelId}`;\n\n/**\n * Formats a channel link for a guild channel.\n *\n * @typeParam ChannelId - This is inferred by the supplied channel id\n * @typeParam GuildId - This is inferred by the supplied guild id\n * @param channelId - The channel's id\n * @param guildId - The guild's id\n */\nexport function channelLink<ChannelId extends Snowflake, GuildId extends Snowflake>(\n\tchannelId: ChannelId,\n\tguildId: GuildId,\n): `https://discord.com/channels/${GuildId}/${ChannelId}`;\n\nexport function channelLink<ChannelId extends Snowflake, GuildId extends Snowflake>(\n\tchannelId: ChannelId,\n\tguildId?: GuildId,\n): `https://discord.com/channels/@me/${ChannelId}` | `https://discord.com/channels/${GuildId}/${ChannelId}` {\n\treturn `https://discord.com/channels/${guildId ?? '@me'}/${channelId}`;\n}\n\n/**\n * Formats a message link for a direct message channel.\n *\n * @typeParam ChannelId - This is inferred by the supplied channel id\n * @typeParam MessageId - This is inferred by the supplied message id\n * @param channelId - The channel's id\n * @param messageId - The message's id\n */\nexport function messageLink<ChannelId extends Snowflake, MessageId extends Snowflake>(\n\tchannelId: ChannelId,\n\tmessageId: MessageId,\n): `https://discord.com/channels/@me/${ChannelId}/${MessageId}`;\n\n/**\n * Formats a message link for a guild channel.\n *\n * @typeParam ChannelId - This is inferred by the supplied channel id\n * @typeParam MessageId - This is inferred by the supplied message id\n * @typeParam GuildId - This is inferred by the supplied guild id\n * @param channelId - The channel's id\n * @param messageId - The message's id\n * @param guildId - The guild's id\n */\nexport function messageLink<ChannelId extends Snowflake, MessageId extends Snowflake, GuildId extends Snowflake>(\n\tchannelId: ChannelId,\n\tmessageId: MessageId,\n\tguildId: GuildId,\n): `https://discord.com/channels/${GuildId}/${ChannelId}/${MessageId}`;\n\nexport function messageLink<ChannelId extends Snowflake, MessageId extends Snowflake, GuildId extends Snowflake>(\n\tchannelId: ChannelId,\n\tmessageId: MessageId,\n\tguildId?: GuildId,\n):\n\t| `https://discord.com/channels/@me/${ChannelId}/${MessageId}`\n\t| `https://discord.com/channels/${GuildId}/${ChannelId}/${MessageId}` {\n\treturn `${guildId === undefined ? channelLink(channelId) : channelLink(channelId, guildId)}/${messageId}`;\n}\n\n/**\n * The heading levels for expanded markdown.\n */\nexport enum HeadingLevel {\n\t/**\n\t * The first heading level.\n\t */\n\tOne = 1,\n\t/**\n\t * The second heading level.\n\t */\n\tTwo,\n\t/**\n\t * The third heading level.\n\t */\n\tThree,\n}\n\n/**\n * Formats the content into a heading level.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n * @param level - The heading level\n */\nexport function heading<Content extends string>(content: Content, level?: HeadingLevel.One): `# ${Content}`;\n\n/**\n * Formats the content into a heading level.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n * @param level - The heading level\n */\nexport function heading<Content extends string>(content: Content, level: HeadingLevel.Two): `## ${Content}`;\n\n/**\n * Formats the content into a heading level.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n * @param level - The heading level\n */\nexport function heading<Content extends string>(content: Content, level: HeadingLevel.Three): `### ${Content}`;\n\nexport function heading(content: string, level?: HeadingLevel) {\n\tswitch (level) {\n\t\tcase HeadingLevel.Three:\n\t\t\treturn `### ${content}`;\n\t\tcase HeadingLevel.Two:\n\t\t\treturn `## ${content}`;\n\t\tdefault:\n\t\t\treturn `# ${content}`;\n\t}\n}\n\n/**\n * A type that recursively traverses into arrays.\n */\nexport type RecursiveArray<ItemType> = readonly (ItemType | RecursiveArray<ItemType>)[];\n\n/**\n * Callback function for list formatters.\n *\n * @internal\n */\nfunction listCallback(element: RecursiveArray<string>, startNumber?: number, depth = 0): string {\n\tif (Array.isArray(element)) {\n\t\treturn element.map((element) => listCallback(element, startNumber, depth + 1)).join('\\n');\n\t}\n\n\treturn `${'  '.repeat(depth - 1)}${startNumber ? `${startNumber}.` : '-'} ${element}`;\n}\n\n/**\n * Formats the elements in the array to an ordered list.\n *\n * @param list - The array of elements to list\n * @param startNumber - The starting number for the list\n */\nexport function orderedList(list: RecursiveArray<string>, startNumber = 1): string {\n\treturn listCallback(list, Math.max(startNumber, 1));\n}\n\n/**\n * Formats the elements in the array to an unordered list.\n *\n * @param list - The array of elements to list\n */\nexport function unorderedList(list: RecursiveArray<string>): string {\n\treturn listCallback(list);\n}\n\n/**\n * Formats the content into a subtext.\n *\n * @typeParam Content - This is inferred by the supplied content\n * @param content - The content to wrap\n */\nexport function subtext<Content extends string>(content: Content): `-# ${Content}` {\n\treturn `-# ${content}`;\n}\n\n/**\n * Formats a date into a short date-time string.\n *\n * @param date - The date to format. Defaults to the current time\n */\nexport function time(date?: Date): `<t:${bigint}>`;\n\n/**\n * Formats a date given a format style.\n *\n * @typeParam Style - This is inferred by the supplied {@link TimestampStylesString}\n * @param date - The date to format\n * @param style - The style to use\n */\nexport function time<Style extends TimestampStylesString>(date: Date, style: Style): `<t:${bigint}:${Style}>`;\n\n/**\n * Formats the given timestamp into a short date-time string.\n *\n * @typeParam Seconds - This is inferred by the supplied timestamp\n * @param seconds - A Unix timestamp in seconds\n */\nexport function time<Seconds extends number>(seconds: Seconds): `<t:${Seconds}>`;\n\n/**\n * Formats the given timestamp into a short date-time string.\n *\n * @typeParam Seconds - This is inferred by the supplied timestamp\n * @typeParam Style - This is inferred by the supplied {@link TimestampStylesString}\n * @param seconds - A Unix timestamp in seconds\n * @param style - The style to use\n */\nexport function time<Seconds extends number, Style extends TimestampStylesString>(\n\tseconds: Seconds,\n\tstyle: Style,\n): `<t:${Seconds}:${Style}>`;\n\nexport function time(timeOrSeconds?: Date | number, style?: TimestampStylesString): string {\n\tif (typeof timeOrSeconds !== 'number') {\n\t\t// eslint-disable-next-line no-param-reassign\n\t\ttimeOrSeconds = Math.floor((timeOrSeconds?.getTime() ?? Date.now()) / 1_000);\n\t}\n\n\treturn typeof style === 'string' ? `<t:${timeOrSeconds}:${style}>` : `<t:${timeOrSeconds}>`;\n}\n\n/**\n * Formats an application directory link.\n *\n * @typeParam ApplicationId - This is inferred by the supplied application id\n * @param applicationId - The application id\n */\nexport function applicationDirectory<ApplicationId extends Snowflake>(\n\tapplicationId: ApplicationId,\n): `https://discord.com/application-directory/${ApplicationId}/store`;\n\n/**\n * Formats an application directory SKU link.\n *\n * @typeParam ApplicationId - This is inferred by the supplied application id\n * @typeParam SKUId - This is inferred by the supplied SKU id\n * @param applicationId - The application id\n * @param skuId - The SKU id\n */\nexport function applicationDirectory<ApplicationId extends Snowflake, SKUId extends Snowflake>(\n\tapplicationId: ApplicationId,\n\tskuId: SKUId,\n): `https://discord.com/application-directory/${ApplicationId}/store/${SKUId}`;\n\nexport function applicationDirectory<ApplicationId extends Snowflake, SKUId extends Snowflake>(\n\tapplicationId: ApplicationId,\n\tskuId?: SKUId,\n):\n\t| `https://discord.com/application-directory/${ApplicationId}/store/${SKUId}`\n\t| `https://discord.com/application-directory/${ApplicationId}/store` {\n\tconst url = `https://discord.com/application-directory/${applicationId}/store` as const;\n\treturn skuId ? `${url}/${skuId}` : url;\n}\n\n/**\n * The {@link https://discord.com/developers/docs/reference#message-formatting-timestamp-styles | message formatting timestamp styles}\n * supported by Discord.\n */\nexport const TimestampStyles = {\n\t/**\n\t * Short time format, consisting of hours and minutes.\n\t *\n\t * @example `16:20`\n\t */\n\tShortTime: 't',\n\n\t/**\n\t * Long time format, consisting of hours, minutes, and seconds.\n\t *\n\t * @example `16:20:30`\n\t */\n\tLongTime: 'T',\n\n\t/**\n\t * Short date format, consisting of day, month, and year.\n\t *\n\t * @example `20/04/2021`\n\t */\n\tShortDate: 'd',\n\n\t/**\n\t * Long date format, consisting of day, month, and year.\n\t *\n\t * @example `20 April 2021`\n\t */\n\tLongDate: 'D',\n\n\t/**\n\t * Short date-time format, consisting of short date and short time formats.\n\t *\n\t * @example `20 April 2021 16:20`\n\t */\n\tShortDateTime: 'f',\n\n\t/**\n\t * Long date-time format, consisting of long date and short time formats.\n\t *\n\t * @example `Tuesday, 20 April 2021 16:20`\n\t */\n\tLongDateTime: 'F',\n\n\t/**\n\t * Relative time format, consisting of a relative duration format.\n\t *\n\t * @example `2 months ago`\n\t */\n\tRelativeTime: 'R',\n} as const satisfies Record<string, string>;\n\n/**\n * The possible {@link TimestampStyles} values.\n */\nexport type TimestampStylesString = (typeof TimestampStyles)[keyof typeof TimestampStyles];\n\n/**\n * All the available faces from Discord's native slash commands.\n */\nexport enum Faces {\n\t/**\n\t * `¯\\_(ツ)_/¯`\n\t */\n\tShrug = '¯\\\\_(ツ)_/¯',\n\n\t/**\n\t * `(╯°□°)╯︵ ┻━┻`\n\t */\n\tTableflip = '(╯°□°)╯︵ ┻━┻',\n\n\t/**\n\t * `┬─┬ノ( º _ ºノ)`\n\t */\n\tUnflip = '┬─┬ノ( º _ ºノ)',\n}\n\n/**\n * All the available guild navigation mentions.\n */\nexport enum GuildNavigationMentions {\n\t/**\n\t * Browse Channels tab.\n\t */\n\tBrowse = '<id:browse>',\n\t/**\n\t * Customize tab with the server's {@link https://discord.com/developers/docs/resources/guild#guild-onboarding-object | onboarding prompts}.\n\t */\n\tCustomize = '<id:customize>',\n\t/**\n\t * {@link https://support.discord.com/hc/articles/13497665141655 | Server Guide} tab.\n\t */\n\tGuide = '<id:guide>',\n}\n", "export * from './escapers.js';\nexport * from './formatters.js';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/formatters#readme | @discordjs/formatters} version\n * that you are currently using.\n *\n * @privateRemarks This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild.\n */\nexport const version = '0.6.0' as string;\n"], "mappings": ";;;;AA8GO,SAAS,eAAe,MAAc,UAAiC,CAAC,GAAW;AACzF,QAAM;AAAA,IACL,WAAAA,aAAY;AAAA,IACZ,YAAAC,cAAa;AAAA,IACb,MAAAC,QAAO;AAAA,IACP,QAAAC,UAAS;AAAA,IACT,WAAAC,aAAY;AAAA,IACZ,eAAAC,iBAAgB;AAAA,IAChB,SAAAC,WAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,SAAAC,WAAU;AAAA,IACV,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,EACd,IAAI;AAEJ,MAAI,CAAC,kBAAkB;AACtB,WAAO,KACL,MAAM,KAAK,EACX,IAAI,CAAC,WAAW,OAAO,UAAU;AACjC,UAAI,QAAQ,KAAK,UAAU,MAAM,SAAS,EAAG,QAAO;AACpD,aAAO,eAAe,WAAW;AAAA,QAChC,YAAAN;AAAA,QACA,MAAAC;AAAA,QACA,QAAAC;AAAA,QACA,WAAAC;AAAA,QACA,eAAAC;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF,CAAC,EACA,KAAKP,aAAY,cAAc,KAAK;AAAA,EACvC;AAEA,MAAI,CAAC,mBAAmB;AACvB,WAAO,KACL,MAAM,yBAAyB,EAC/B,IAAI,CAAC,WAAW,OAAO,UAAU;AACjC,UAAI,QAAQ,KAAK,UAAU,MAAM,SAAS,EAAG,QAAO;AACpD,aAAO,eAAe,WAAW;AAAA,QAChC,WAAAA;AAAA,QACA,MAAAE;AAAA,QACA,QAAAC;AAAA,QACA,WAAAC;AAAA,QACA,eAAAC;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF,CAAC,EACA,KAAKN,cAAa,QAAQ,GAAG;AAAA,EAChC;AAEA,MAAI,MAAM;AACV,MAAI,OAAQ,OAAM,aAAa,GAAG;AAClC,MAAIA,YAAY,OAAM,iBAAiB,GAAG;AAC1C,MAAID,WAAW,OAAM,gBAAgB,GAAG;AACxC,MAAIG,QAAQ,OAAM,aAAa,GAAG;AAClC,MAAID,MAAM,OAAM,WAAW,GAAG;AAC9B,MAAIE,WAAW,OAAM,gBAAgB,GAAG;AACxC,MAAIC,eAAe,OAAM,oBAAoB,GAAG;AAChD,MAAIC,SAAS,OAAM,cAAc,GAAG;AACpC,MAAIC,SAAS,OAAM,cAAc,GAAG;AACpC,MAAI,aAAc,OAAM,mBAAmB,GAAG;AAC9C,MAAI,aAAc,OAAM,mBAAmB,GAAG;AAC9C,MAAI,WAAY,OAAM,iBAAiB,GAAG;AAC1C,SAAO;AACR;AA7EgB;AAoFT,SAAS,gBAAgB,MAAsB;AACrD,SAAO,KAAK,WAAW,OAAO,WAAW;AAC1C;AAFgB;AAST,SAAS,iBAAiB,MAAsB;AACtD,SAAO,KAAK,WAAW,6BAA6B,CAAC,UAAW,MAAM,WAAW,IAAI,WAAW,KAAM;AACvG;AAFgB;AAST,SAAS,aAAa,MAAsB;AAClD,MAAI,MAAM;AACV,QAAM,UAAU,KAAK,WAAW,+BAA+B,CAAC,GAAG,UAAU;AAC5E,QAAI,UAAU,KAAM,QAAO,EAAE,MAAM,IAAI,MAAM,KAAK,KAAK,GAAG,KAAK;AAC/D,WAAO,MAAM,KAAK;AAAA,EACnB,CAAC;AACD,QAAM;AACN,SAAO,QAAQ,WAAW,+DAA+D,CAAC,GAAG,UAAU;AACtG,QAAI,UAAU,KAAM,QAAO,EAAE,MAAM,IAAI,MAAM,KAAK,KAAK,GAAG,KAAK;AAC/D,WAAO,MAAM,KAAK;AAAA,EACnB,CAAC;AACF;AAXgB;AAkBT,SAAS,WAAW,MAAsB;AAChD,MAAI,MAAM;AACV,SAAO,KAAK,WAAW,cAAc,CAAC,GAAG,UAAU;AAClD,QAAI,MAAO,QAAO,EAAE,MAAM,IAAI,GAAG,KAAK,WAAW,SAAS,KAAK;AAC/D,WAAO;AAAA,EACR,CAAC;AACF;AANgB;AAaT,SAAS,gBAAgB,MAAsB;AACrD,MAAI,MAAM;AACV,SAAO,KAAK,WAAW,8CAA8C,CAAC,GAAG,UAAU;AAClF,QAAI,MAAO,QAAO,EAAE,MAAM,IAAI,GAAG,KAAK,WAAW,SAAS,KAAK;AAC/D,WAAO;AAAA,EACR,CAAC;AACF;AANgB;AAaT,SAAS,oBAAoB,MAAsB;AACzD,SAAO,KAAK,WAAW,MAAM,QAAQ;AACtC;AAFgB;AAST,SAAS,cAAc,MAAsB;AACnD,SAAO,KAAK,WAAW,MAAM,QAAQ;AACtC;AAFgB;AAST,SAAS,aAAa,MAAsB;AAClD,SAAO,KAAK,WAAW,MAAM,MAAM;AACpC;AAFgB;AAST,SAAS,cAAc,MAAsB;AACnD,SAAO,KAAK,WAAW,oCAAoC,YAAY;AACxE;AAFgB;AAST,SAAS,mBAAmB,MAAsB;AACxD,SAAO,KAAK,WAAW,qBAAqB,UAAU;AACvD;AAFgB;AAST,SAAS,mBAAmB,MAAsB;AACxD,SAAO,KAAK,WAAW,gBAAgB,OAAO;AAC/C;AAFgB;AAST,SAAS,iBAAiB,MAAsB;AACtD,SAAO,KAAK,WAAW,iBAAiB,MAAM;AAC/C;AAFgB;;;AC9RT,SAAS,UAAU,UAAkB,SAA0B;AACrE,SAAO,YAAY,SAAY;AAAA,EAAW,QAAQ;AAAA,UAAa,SAAS,QAAQ;AAAA,EAAK,OAAO;AAAA;AAC7F;AAFgB;AAUT,SAAS,WAAmC,SAAoC;AACtF,SAAO,KAAK,OAAO;AACpB;AAFgB;AAUT,SAAS,OAA+B,SAAkC;AAChF,SAAO,IAAI,OAAO;AACnB;AAFgB;AAUT,SAAS,KAA6B,SAAoC;AAChF,SAAO,KAAK,OAAO;AACpB;AAFgB;AAWT,SAAS,WAAmC,SAAoC;AACtF,SAAO,UAAU,OAAO;AACzB;AAFgB;AAUT,SAAS,UAAkC,SAAoC;AACrF,SAAO,KAAK,OAAO;AACpB;AAFgB;AAUT,SAAS,cAAsC,SAAoC;AACzF,SAAO,KAAK,OAAO;AACpB;AAFgB;AAWT,SAAS,MAA8B,SAAkC;AAC/E,SAAO,KAAK,OAAO;AACpB;AAFgB;AAWT,SAAS,WAAmC,SAAoC;AACtF,SAAO,OAAO,OAAO;AACtB;AAFgB;AAmBT,SAAS,cAAc,KAAmB;AAChD,SAAO,IAAI,GAAG;AACf;AAFgB;AAyDT,SAAS,UAAU,SAAiB,KAAmB,OAAgB;AAC7E,SAAO,QAAQ,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,GAAG;AACvE;AAFgB;AAUT,SAAS,QAAgC,SAAoC;AACnF,SAAO,KAAK,OAAO;AACpB;AAFgB;AAUT,SAAS,YAAsC,QAAgC;AACrF,SAAO,KAAK,MAAM;AACnB;AAFgB;AAUT,SAAS,eAA4C,WAAyC;AACpG,SAAO,KAAK,SAAS;AACtB;AAFgB;AAUT,SAAS,YAAsC,QAAiC;AACtF,SAAO,MAAM,MAAM;AACpB;AAFgB;AA6DT,SAAS,mCAMf,aACA,qBACA,gBACA,WAI6C;AAC7C,MAAI,cAAc,QAAW;AAC5B,WAAO,KAAK,WAAW,IAAI,mBAAmB,IAAI,cAAe,IAAI,SAAS;AAAA,EAC/E;AAEA,MAAI,mBAAmB,QAAW;AACjC,WAAO,KAAK,WAAW,IAAI,mBAAmB,IAAI,cAAc;AAAA,EACjE;AAEA,SAAO,KAAK,WAAW,IAAI,mBAAmB;AAC/C;AAvBgB;AAuFT,SAAS,YACf,kBACA,UACyD;AACzD,QAAM,UACL,OAAO,qBAAqB,WACzB;AAAA,IACA,IAAI;AAAA,IACJ,UAAU,YAAY;AAAA,EACvB,IACC;AAEJ,QAAM,EAAE,IAAI,UAAU,YAAY,MAAM,UAAU,IAAI;AAEtD,SAAO,IAAI,aAAa,MAAM,EAAE,IAAI,aAAa,GAAG,IAAI,EAAE;AAC3D;AAfgB;AA6DT,SAAS,YACf,WACA,SAC2G;AAC3G,SAAO,gCAAgC,WAAW,KAAK,IAAI,SAAS;AACrE;AALgB;AAoCT,SAAS,YACf,WACA,WACA,SAGsE;AACtE,SAAO,GAAG,YAAY,SAAY,YAAY,SAAS,IAAI,YAAY,WAAW,OAAO,CAAC,IAAI,SAAS;AACxG;AARgB;AAaT,IAAK,eAAL,kBAAKC,kBAAL;AAIN,EAAAA,4BAAA,SAAM,KAAN;AAIA,EAAAA,4BAAA;AAIA,EAAAA,4BAAA;AAZW,SAAAA;AAAA,GAAA;AA0CL,SAAS,QAAQ,SAAiB,OAAsB;AAC9D,UAAQ,OAAO;AAAA,IACd,KAAK;AACJ,aAAO,OAAO,OAAO;AAAA,IACtB,KAAK;AACJ,aAAO,MAAM,OAAO;AAAA,IACrB;AACC,aAAO,KAAK,OAAO;AAAA,EACrB;AACD;AATgB;AAqBhB,SAAS,aAAa,SAAiC,aAAsB,QAAQ,GAAW;AAC/F,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,WAAO,QAAQ,IAAI,CAACC,aAAY,aAAaA,UAAS,aAAa,QAAQ,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,EACzF;AAEA,SAAO,GAAG,KAAK,OAAO,QAAQ,CAAC,CAAC,GAAG,cAAc,GAAG,WAAW,MAAM,GAAG,IAAI,OAAO;AACpF;AANS;AAcF,SAAS,YAAY,MAA8B,cAAc,GAAW;AAClF,SAAO,aAAa,MAAM,KAAK,IAAI,aAAa,CAAC,CAAC;AACnD;AAFgB;AAST,SAAS,cAAc,MAAsC;AACnE,SAAO,aAAa,IAAI;AACzB;AAFgB;AAUT,SAAS,QAAgC,SAAmC;AAClF,SAAO,MAAM,OAAO;AACrB;AAFgB;AAyCT,SAAS,KAAK,eAA+B,OAAuC;AAC1F,MAAI,OAAO,kBAAkB,UAAU;AAEtC,oBAAgB,KAAK,OAAO,eAAe,QAAQ,KAAK,KAAK,IAAI,KAAK,GAAK;AAAA,EAC5E;AAEA,SAAO,OAAO,UAAU,WAAW,MAAM,aAAa,IAAI,KAAK,MAAM,MAAM,aAAa;AACzF;AAPgB;AAgCT,SAAS,qBACf,eACA,OAGqE;AACrE,QAAM,MAAM,6CAA6C,aAAa;AACtE,SAAO,QAAQ,GAAG,GAAG,IAAI,KAAK,KAAK;AACpC;AARgB;AAcT,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,cAAc;AACf;AAUO,IAAK,QAAL,kBAAKC,WAAL;AAIN,EAAAA,OAAA,WAAQ;AAKR,EAAAA,OAAA,eAAY;AAKZ,EAAAA,OAAA,YAAS;AAdE,SAAAA;AAAA,GAAA;AAoBL,IAAK,0BAAL,kBAAKC,6BAAL;AAIN,EAAAA,yBAAA,YAAS;AAIT,EAAAA,yBAAA,eAAY;AAIZ,EAAAA,yBAAA,WAAQ;AAZG,SAAAA;AAAA,GAAA;;;AC9tBL,IAAM,UAAU;", "names": ["codeBlock", "inlineCode", "bold", "italic", "underline", "strikethrough", "spoiler", "heading", "HeadingLevel", "element", "Faces", "GuildNavigationMentions"]}