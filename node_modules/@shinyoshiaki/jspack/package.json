{"author": "https://github.com/pgriess", "name": "@shinyoshiaki/jspack", "description": "JavaScript library to pack primitives to octet arrays, including int64 support, packaged for NodeJS.", "version": "0.0.6", "homepage": "https://github.com/birchroad/node-jspack", "repository": {"type": "git", "url": "git://github.com/birchroad/node-jspack.git"}, "main": "./jspack.js", "types": "./jspack.d.ts", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "http://github.com/birchroad/node-jspack"}, {"name": "<PERSON>", "web": "https://github.com/AndreasAntener/node-jspack"}], "devDependencies": {"jscs": "^1.9.0", "long": "", "mocha": "", "should": "", "sinon": ""}, "scripts": {"pretest": "npm install", "test": "mocha test", "lint": "jscs jspack.js ./test/"}, "publishConfig": {"access": "public"}}