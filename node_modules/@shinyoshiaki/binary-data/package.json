{"name": "@shinyoshiaki/binary-data", "version": "0.6.1", "description": "Declarative binary data encoder / decoder.", "main": "src/index.js", "types": "./binary-data.d.ts", "scripts": {"lint": "npx eslint .", "cover": "npx jest --coverage", "test": "npm run lint && npx jest", "test-ci": "npm run lint && npm run cover"}, "repository": {"type": "git", "url": "git+https://github.com/reklatsmasters/binary-data.git"}, "keywords": ["binary", "data", "buffer", "stream", "decode", "encode", "bl", "bin-protocol", "restructure", "varstruct"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/reklatsmasters/binary-data/issues"}, "homepage": "https://github.com/reklatsmasters/binary-data#readme", "engines": {"node": ">=6"}, "dependencies": {"generate-function": "^2.3.1", "is-plain-object": "^2.0.3"}, "devDependencies": {"@nodertc/eslint-config": "0.2.1", "binary": "^0.3.0", "bl": "^2.1.2", "eslint": "^5.6.1", "jest": "^23.6.0", "prettier": "^1.14.3"}, "jest": {"modulePaths": ["<rootDir>", "<rootDir>/src", "<rootDir>/src/node_modules"], "testMatch": ["**/test/**/*.js"], "testPathIgnorePatterns": ["<rootDir>/node_modules/"], "coverageDirectory": "<rootDir>/coverage", "collectCoverageFrom": ["**/src/*.js", "**/src/node_modules/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/"], "cacheDirectory": ".jest-cache", "haste": {"providesModuleNodeModules": ["internal", "lib", "streams", "types"]}}, "eslintConfig": {"extends": "@nodertc", "overrides": [{"files": ["test/**/*.js"], "env": {"jest": true}, "settings": {"import/resolver": {"node": {"moduleDirectory": ["node_modules", "src", "src/node_modules"]}}}, "rules": {"require-jsdoc": "off"}}, {"files": ["benchmark/*.js", "examples/*.js"], "rules": {"no-console": "off", "require-jsdoc": "off", "no-plusplus": "off", "no-param-reassign": "off"}}]}, "publishConfig": {"access": "public"}}