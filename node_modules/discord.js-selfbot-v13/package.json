{"name": "discord.js-selfbot-v13", "version": "3.6.1", "description": "A unofficial discord.js fork for creating selfbots [Based on discord.js v13]", "main": "./src/index.js", "types": "./typings/index.d.ts", "scripts": {"all": "npm run build && npm publish", "test": "npm run lint:all && npm run docs:test && npm run test:typescript", "fix:all": "npm run lint:fix && npm run lint:typings:fix && npm run format", "test:typescript": "tsc --noEmit && tsd", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:typings": "tslint typings/index.d.ts", "lint:typings:fix": "tslint typings/index.d.ts --fix", "format": "prettier --write src/**/*.js typings/**/*.ts", "lint:all": "npm run lint &&  npm run lint:typings", "docs": "docgen --source src --custom docs/index.yml --output docs/main.json", "docs:test": "docgen --source src --custom docs/index.yml", "build": "npm run lint:fix && npm run lint:typings:fix && npm run format && npm run docs"}, "files": ["src", "typings"], "directories": {"lib": "src", "test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/aiko-chan-ai/discord.js-selfbot-v13.git"}, "keywords": ["discord.js", "discord.js v13", "selfbot", "selfbot v13", "djs", "api", "bot", "node", "discord", "client", "discordapp"], "author": "aiko-chan-ai", "license": "GNU General Public License v3.0", "bugs": {"url": "https://github.com/aiko-chan-ai/discord.js-selfbot-v13/issues"}, "homepage": "https://github.com/aiko-chan-ai/discord.js-selfbot-v13#readme", "dependencies": {"@discordjs/builders": "^1.6.3", "@discordjs/collection": "^1.5.3", "@sapphire/async-queue": "^1.5.5", "@sapphire/shapeshift": "^3.9.5", "discord-api-types": "^0.37.117", "fetch-cookie": "^2.1.0", "find-process": "^1.4.10", "prism-media": "^1.3.5", "qrcode": "^1.5.4", "tough-cookie": "^4.1.4", "tree-kill": "^1.2.2", "undici": "^6.21.0", "werift-rtp": "^0.8.4", "ws": "^8.16.0"}, "engines": {"node": ">=20.18"}, "devDependencies": {"@commitlint/cli": "^16.0.1", "@commitlint/config-angular": "^16.0.0", "@discordjs/docgen": "^0.11.1", "@favware/npm-deprecate": "^1.0.7", "@types/debug": "^4.1.12", "@types/node": "^22.10.7", "@types/ws": "^8.5.10", "conventional-changelog-cli": "^2.2.2", "dtslint": "^4.2.1", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "husky": "^7.0.4", "is-ci": "^3.0.1", "jest": "^28.1.3", "lint-staged": "^12.1.4", "prettier": "^2.8.8", "tsd": "^0.28.1", "tslint": "^6.1.3", "typescript": "^5.5.4"}}