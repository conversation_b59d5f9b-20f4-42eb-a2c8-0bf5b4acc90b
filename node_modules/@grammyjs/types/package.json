{"name": "@grammyjs/types", "version": "3.21.0", "description": "Telegram Bot API type declarations for grammY", "main": "mod.js", "repository": {"type": "git", "url": "git+https://github.com/grammyjs/types.git"}, "keywords": ["grammy", "telegram", "bot", "api", "types"], "scripts": {"prepare": "deno task build"}, "author": "KnorpelSenf", "types": "mod.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/grammyjs/types/issues"}, "files": ["*.d.ts", "mod.js"], "homepage": "https://grammy.dev/", "devDependencies": {"deno-bin": "^1.31.1"}}