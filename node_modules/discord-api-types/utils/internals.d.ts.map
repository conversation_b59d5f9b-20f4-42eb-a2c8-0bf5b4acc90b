{"version": 3, "file": "internals.d.ts", "sourceRoot": "", "sources": ["internals.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,QAAQ,CAAC,CAAC,IAAI;KACxB,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;CAC3B,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI;KACjC,CAAC,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACjC,CAAC;AAEF,MAAM,MAAM,oDAAoD,CAAC,IAAI,IAAI;KACvE,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAC7D,oDAAoD,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAC5D,oDAAoD,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;CAC3E,CAAC;AAEF,MAAM,MAAM,aAAa,CAAC,IAAI,IAAI,oDAAoD,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAEtG,MAAM,MAAM,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC;KAAG,CAAC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;CAAE,CAAC,CAAC;AAEhG,MAAM,MAAM,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;AAEvH,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AACvB,KAAK,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC/D;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,EAAE,CAAC,SAAS,gBAAgB,CAAC,CAAC,CAAC,IAC5D,CAAC,SAAS,OAAO,GAChB,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,KAAK,GAC9B,KAAK,GACJ;KAAG,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAC7C,KAAK,CAAC;AAET,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAEhD;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,EAAE,CAAC,SAAS,gBAAgB,CAAC,CAAC,CAAC,IAC5D,CAAC,SAAS,OAAO,GAAG;KAAG,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG,KAAK,CAAC;AAE1E,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAKhD,eAAO,MAAM,iBAAiB;gBACjB,MAAM;CAOlB,CAAC"}